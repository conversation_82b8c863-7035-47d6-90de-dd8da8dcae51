document.addEventListener('DOMContentLoaded', function() {
    const benefitsLineSections = document.querySelectorAll('.internship-benefits-line');

    benefitsLineSections.forEach(section => {
        const benefitItems = section.querySelectorAll('.benefit-line-item');
        const descriptionContainers = section.querySelectorAll('.benefit-description-container');
        const progressLines = section.querySelectorAll('.benefit-line');

        if (!benefitItems.length || !descriptionContainers.length) return;

        let currentIndex = 0;
        let autoProgressTimer = null;
        let isAutoProgressing = true;
        let isTransitioning = false;

        // Calculate and set line heights based on content
        function updateLineHeights() {
            benefitItems.forEach((item, index) => {
                const line = item.querySelector('.benefit-line');
                if (line && index < benefitItems.length - 1) {
                    const descriptionContainer = item.querySelector('.benefit-description-container');
                    const isOpen = descriptionContainer && descriptionContainer.classList.contains('max-h-96');

                    if (isOpen) {
                        // When content is open, calculate dynamic height
                        const currentItemRect = item.getBoundingClientRect();
                        const nextItem = benefitItems[index + 1];
                        const nextItemRect = nextItem.getBoundingClientRect();
                        const lineHeight = Math.max(nextItemRect.top - currentItemRect.top - 48, 57);
                        line.style.height = lineHeight + 'px';
                    } else {
                        // When content is closed, use fixed height
                        line.style.height = '57px';
                    }
                }
            });
        }

        // Initialize first item
        showBenefit(0, false); // false = no animation on initial load

        // Auto-progression functionality
        function startAutoProgress() {
            if (!isAutoProgressing || isTransitioning) return;

            // Start filling the current line
            const currentLine = benefitItems[currentIndex]?.querySelector('.progress-fill-vertical');
            if (currentLine) {
                currentLine.style.height = '100%';

                // After line fills, transition to next benefit
                setTimeout(() => {
                    if (isAutoProgressing && !isTransitioning) {
                        const nextIndex = (currentIndex + 1) % benefitItems.length;
                        transitionToNextBenefit(nextIndex);
                    }
                }, 4500); // 4.5 seconds for line to fill
            }
        }

        // Handle smooth transition between benefits
        function transitionToNextBenefit(nextIndex) {
            isTransitioning = true;

            // First, close current benefit
            const currentContainer = descriptionContainers[currentIndex];
            if (currentContainer) {
                currentContainer.classList.add('max-h-0');
                currentContainer.classList.remove('max-h-96');
            }

            // Update line height for closed state
            setTimeout(() => {
                updateLineHeights();
            }, 100);

            // After current closes, open next benefit
            setTimeout(() => {
                currentIndex = nextIndex;
                const nextContainer = descriptionContainers[nextIndex];
                if (nextContainer) {
                    nextContainer.classList.remove('max-h-0');
                    nextContainer.classList.add('max-h-96');
                }

                // Reset all progress lines for new cycle
                benefitItems.forEach((item, i) => {
                    const progressFill = item.querySelector('.progress-fill-vertical');
                    if (progressFill) {
                        if (i < nextIndex) {
                            progressFill.style.height = '100%';
                        } else {
                            progressFill.style.height = '0%';
                        }
                    }
                });

                // Update line heights for new open state
                setTimeout(() => {
                    updateLineHeights();
                    isTransitioning = false;

                    // Continue auto-progression
                    setTimeout(() => {
                        startAutoProgress();
                    }, 500); // Small delay before starting next cycle
                }, 700); // Wait for opening animation to complete

            }, 700); // Wait for closing animation to complete
        }

        function stopAutoProgress() {
            if (autoProgressTimer) {
                clearTimeout(autoProgressTimer);
                autoProgressTimer = null;
            }
        }

        function showBenefit(index, animate = true) {
            if (animate) {
                isTransitioning = true;
                transitionToNextBenefit(index);
                return;
            }

            // Update current index
            currentIndex = index;

            // Update description visibility (for initial load only)
            descriptionContainers.forEach((container, i) => {
                if (i === index) {
                    container.classList.remove('max-h-0');
                    container.classList.add('max-h-96');
                } else {
                    container.classList.add('max-h-0');
                    container.classList.remove('max-h-96');
                }
            });

            // Update vertical progress lines (for initial load only)
            benefitItems.forEach((item, i) => {
                const progressFill = item.querySelector('.progress-fill-vertical');
                if (progressFill) {
                    if (i < index) {
                        // Fill completed items
                        progressFill.style.height = '100%';
                    } else {
                        // Reset current and future items
                        progressFill.style.height = '0%';
                    }
                }
            });

            // Update line heights for initial state
            updateLineHeights();
        }

        // Click handlers for manual navigation
        benefitItems.forEach((item, index) => {
            item.addEventListener('click', () => {
                if (isTransitioning) return; // Prevent clicks during transitions

                // Stop auto-progression when user clicks
                isAutoProgressing = false;
                stopAutoProgress();
                showBenefit(index, true);

                // Resume auto-progression after 5 seconds of inactivity
                setTimeout(() => {
                    isAutoProgressing = true;
                    startAutoProgress();
                }, 5000);
            });
        });

        // Start auto-progression
        startAutoProgress();

        // Pause auto-progression when user hovers over the section
        section.addEventListener('mouseenter', () => {
            stopAutoProgress();
        });

        section.addEventListener('mouseleave', () => {
            if (isAutoProgressing) {
                startAutoProgress();
            }
        });

        // Handle visibility change (pause when tab is not active)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                stopAutoProgress();
            } else if (isAutoProgressing) {
                startAutoProgress();
            }
        });

        // Handle window resize to recalculate line heights
        window.addEventListener('resize', () => {
            updateLineHeights();
        });
    });
});
